<template>
  <div class="container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item prop="yyId">
        <el-select
          v-model="queryParams.yyId"
          placeholder="所属应用"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="(item, i) in appOptions"
            :key="i"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="deptId">
        <treeselect
          style="width: 160px; height: 32px"
          v-model="queryParams.deptId"
          :options="enabledDeptOptions"
          :show-count="true"
          placeholder="请选择归属部门"
        />
      </el-form-item>
      <el-form-item prop="dateArr">
        <el-date-picker
          v-model="queryParams.dateArr"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <div class="card">
      <div class="cardTitle">应用监测告警</div>
      <yyjk :yyId="queryParams.yyId" :deptId="queryParams.deptId" :dateArr="queryParams.dateArr"></yyjk>
    </div>
    <div class="card">
      <div class="cardTitle">安全隐患告警</div>
      <aqyh :yyId="queryParams.yyId" :deptId="queryParams.deptId" :dateArr="queryParams.dateArr"></aqyh>
    </div>
    <div class="card">
      <div class="cardTitle">云资源告警</div>
      <yzy :yyId="queryParams.yyId" :deptId="queryParams.deptId" :dateArr="queryParams.dateArr"></yzy>
    </div>
    <div class="card">
      <div class="cardTitle">数据库告警</div>
      <sjk :yyId="queryParams.yyId" :deptId="queryParams.deptId" :dateArr="queryParams.dateArr"></sjk>
    </div>
  </div>
</template>

<script>
import yyjk from "@/views/warning/warningList/components/yyjk.vue";
import yzy from "@/views/warning/warningList/components/yzy.vue";
import aqyh from "@/views/warning/warningList/components/aqyh.vue";
import sjk from "@/views/warning/warningList/components/sjk.vue";
import { listAllYy, deptTreeSelect } from "@/api/serve/orderlist";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  components: { yyjk, yzy, aqyh, sjk, Treeselect },
  data() {
    return {
      queryParams: {
        yyId: undefined,
        deptId: undefined,
        dateArr:[],
      },
      appOptions: [],
      enabledDeptOptions: [],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data;
      });
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
::v-deep .el-table__cell {
  padding: 6px 0;
}
::v-deep .vue-treeselect__control {
  height: 32px;
  box-sizing: border-box;
}
</style>
