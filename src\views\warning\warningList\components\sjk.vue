<template>
  <div>
    <el-table :data="datalist">
      <!-- <el-table-column prop="ip" label="主机IP" align="center" /> -->
      <el-table-column prop="departmentName" label="关联部门" align="center" />
      <el-table-column prop="yyName" label="关联应用" align="center" />
      <el-table-column prop="" label="告警等级" align="center" width="100">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.level == 2
                  ? 'tag_red'
                  : scope.row.level == 1
                  ? 'tag_yel'
                  : ''
              "
            >
              {{
                scope.row.level == 2
                  ? "紧急"
                  : scope.row.level == 1
                  ? "一般"
                  : "-"
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="告警时间" align="center" />
      <el-table-column label="告警状态" align="center">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.status == 2
                  ? 'tag_yel'
                  : scope.row.status == 1
                  ? 'tag_gre'
                  : ''
              "
            >
              {{
                scope.row.status == 1
                  ? "已完成"
                  : scope.row.status == 0
                  ? "未完成"
                  : "-"
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="告警内容" align="center" />
      <el-table-column prop="" label="处置方式" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.czfs == 1">忽略</div>
          <div v-if="scope.row.czfs == 2">已处置</div>
        </template></el-table-column
      >
      <!-- <el-table-column prop="dbId" label="数据库id" align="center" /> -->
      <!-- <el-table-column prop="instance" label="实例名称" align="center" /> -->
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          >
          <el-button type="text" disabled v-else>转工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>
    <!-- 详情弹窗 -->
    <infoDialog :show="show" :info="info" @close="show = false"></infoDialog>

    <!-- 转工单弹窗 -->
    <addOrderDialog
      :show="show1"
      :info="info1"
      @close="show1 = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>
  </div>
</template>

<script>
import { listSjkyj } from "@/api/warning/warningList";
import addOrderDialog from "./addOrderDialog.vue";
import infoDialog from "./sjkDialog.vue";
export default {
  props: {
    yyId: {
      type: Number,
      default: null,
    },
    deptId: {
      type: Number,
      default: null,
    },
    dateArr: {
      type: Array,
      default: [],
    },
  },
  components: { addOrderDialog, infoDialog },
  data() {
    return {
      datalist: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      //查看
      show: false,
      info: {},
      //新增工单
      show1: false,
      info1: {},
    };
  },
  watch: {
    yyId(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
    dateArr(newVal, oldVal) {
      this.getList();
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    //新增工单开始--
    showDialog(row) {
      this.show1 = true;
      this.info1 = {
        gdType: "3",
        title: (row.yyName ? row.yyName : "") + "数据库预警",
        describe: row.content,
        priority: 3,
        handlerId: "",
        yyId: row.yyId,
        dataId: row.sjId,
        dataType: 3,
        csList: [],
      };
      this.info1DeptName = row.deptName;
      this.info1DeptId = row.deptId;
    },
    addSuccess() {
      this.show1 = false;
      this.getList();
    },
    //新增工单结束--
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        this.datalist = [];
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          yyId: this.yyId,
          constructionUnitId: this.deptId,
          startTime: this.dateArr[0],
          endTime: this.dateArr[1],
        };
        const response = await listSjkyj(params);
        if (response.code === 200 && response.data) {
          this.datalist = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.datalist = [];
          this.total = 0;
          this.$message.error(response.msg || "获取数据库预警列表失败");
        }
      } catch (error) {
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取数据库预警列表失败");
      }
    },
    goDetail(row) {
      this.info = row;
      this.show = true;
    },
  },
};
</script>


<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
.tag_gre {
  background: #36cbcb1a;
  color: #36cbcb;
}
.tag_grey {
  background: #1e222a0d;
  color: #1d2129;
}

.barBox {
  margin-left: 12px;
  .bar {
    width: 110px;
    height: 6px;
    background: #e0e5ea;
    border-radius: 8px;
    margin-right: 10px;
    .value {
      height: 100%;
      border-radius: 8px;
    }
  }
}
</style>
