import request from "@/utils/request";

// 查询处置预案列表
export function listCzya(query) {
  return request({
    url: "/tyywpt/tTyywCzya/list",
    method: "get",
    params: query,
  });
}

// 获取处置预案详细信息
export function getCzya(query) {
  return request({
    url: "/tyywpt/tTyywCzya/getInfo",
    method: "get",
    params: query,
  });
}

// 新增处置预案
export function addCzya(data) {
  return request({
    url: "/tyywpt/tTyywCzya/add",
    method: "post",
    data: data,
  });
}

// 修改处置预案
export function updateCzya(data) {
  return request({
    url: "/tyywpt/tTyywCzya/edit",
    method: "put",
    data: data,
  });
}

// 删除公告
export function delCzya(query) {
  return request({
    url: "/tyywpt/tTyywCzya/remove",
    method: "delete",
    params: query,
  });
}
