<!--应用反查-->
<template>
  <div class="container">
    <div class="card">
      <div class="cardTitle">软件物料清单</div>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="组件类型" prop="type">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择组件类型"
            clearable
            style="width: 160px"
            @change="onTypeChange"
          >
            <el-option
              v-for="item in componentTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="组件名称" prop="zjmc">
          <el-select
            v-model="queryParams.zjmc"
            clearable
            filterable
            remote
            :remote-method="searchComponents"
            :loading="componentLoading"
            placeholder="输入检索组件"
            style="width: 160px"
            @change="onComponentChange"
          >
            <el-option
              v-for="item in componentOptions"
              :key="item.id"
              :label="item.label"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="bbh">
          <el-select
            v-model="queryParams.bbh"
            placeholder="请选择版本号"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="item in versionOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" align="center" width="80" />
        <el-table-column
          prop="yyName"
          label="应用名称"
          align="center"
          min-width="120"
        />
        <el-table-column
          prop="adminName"
          label="应用管理员"
          align="center"
          min-width="120"
        />
        <el-table-column
          prop="ip"
          label="联系方式"
          align="center"
          min-width="120"
        />
        <el-table-column
          prop="ywcs"
          label="运行单位"
          align="center"
          min-width="120"
        />
        <el-table-column
          prop="zjmc"
          label="组件名称"
          align="center"
          min-width="120"
        />
        <el-table-column prop="type" label="类型" align="center" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.type === 1">中间件</span>
            <span v-else-if="scope.row.type === 2">系统</span>
            <span v-else-if="scope.row.type === 3">数据库</span>
            <span v-else>{{ scope.row.type }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="gys"
          label="供应商"
          align="center"
          min-width="120"
        />
        <el-table-column
          prop="bbh"
          label="版本号"
          align="center"
          min-width="120"
        />
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <!-- <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          > -->
            <el-button type="text" disabled>转工单</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listApplicationBackcheck } from "@/api/property/applicationBackcheck";
import { listSupplyChain } from "@/api/property/supplyChain";

export default {
  name: "ApplicationBackcheck",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      selectedRows: [],
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: "", // 组件类型
        zjmc: "", // 组件名称 - 根据接口参数名
        bbh: "", // 版本号 - 根据接口参数名
      },
      // 组件类型选项
      componentTypeOptions: [
        { label: "中间件", value: 1 },
        { label: "系统", value: 2 },
        { label: "数据库", value: 3 },
      ],
      // 组件选项
      componentOptions: [],
      componentLoading: false,
      // 版本号选项
      versionOptions: [],
      // 当前选中的组件数据
      selectedComponentData: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listApplicationBackcheck(this.queryParams)
        .then((response) => {
          if (response.code === 200) {
            this.tableData = response.data.list || [];
            this.total = response.data.total || 0;
          } else {
            this.tableData = [];
            this.total = 0;
            this.$modal.msgError(response.msg || "查询失败");
          }
          this.loading = false;
        })
        .catch(() => {
          this.tableData = [];
          this.total = 0;
          this.loading = false;
          this.$modal.msgError("查询失败");
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    /** 分页 - 当前页变化 */
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },

    /** 组件类型变化处理 */
    onTypeChange() {
      // 清空组件名称和版本号
      this.queryParams.zjmc = "";
      this.queryParams.bbh = "";
      this.componentOptions = [];
      this.versionOptions = [];
      this.selectedComponentData = [];
    },

    /** 搜索组件 */
    async searchComponents(query) {
      if (query !== "" && this.queryParams.type) {
        this.componentLoading = true;
        try {
          const response = await listSupplyChain({
            pageNum: 1,
            pageSize: 50,
            name: query, // 按名称模糊查询
            type: this.queryParams.type, // 按类型过滤
          });

          if (response.code === 200 && response.data && response.data.list) {
            this.componentOptions = response.data.list.map((item) => ({
              id: item.id,
              name: item.name,
              supplier: item.gys,
              version: item.bbh,
              ip: item.ip,
              label: item.name,
            }));

            // 保存完整的组件数据用于版本号筛选
            this.selectedComponentData = response.data.list;
          } else {
            this.componentOptions = [];
            this.selectedComponentData = [];
          }
        } catch (error) {
          console.error("搜索组件失败:", error);
          this.componentOptions = [];
          this.selectedComponentData = [];
          this.$modal.msgError("搜索组件失败");
        } finally {
          this.componentLoading = false;
        }
      } else {
        this.componentOptions = [];
        this.selectedComponentData = [];
      }
    },

    /** 组件选择变化处理 */
    onComponentChange(componentName) {
      if (componentName) {
        // 根据选中的组件名称，获取该组件的所有版本号
        const componentVersions = this.selectedComponentData
          .filter((item) => item.name === componentName)
          .map((item) => item.bbh)
          .filter((version) => version); // 过滤空版本号

        // 去重
        this.versionOptions = [...new Set(componentVersions)];

        // 清空版本号选择
        this.queryParams.bbh = "";
      } else {
        this.versionOptions = [];
        this.queryParams.bbh = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
