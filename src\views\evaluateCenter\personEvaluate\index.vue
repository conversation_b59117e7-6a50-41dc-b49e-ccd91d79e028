<template>
  <div class="container">
    <img
      src="@/assets/images/evaluateCenter/3.png"
      alt=""
      @click="flag = true"
    />
    <el-dialog
      :visible.sync="flag"
      title="运维人员评价"
      width="800px"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: center">
        <img
          src="@/assets/images/evaluateCenter/33.png"
          alt=""
          style="width: 700px"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      flag: false,
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
  img {
    width: 100%;
  }
}
</style>
