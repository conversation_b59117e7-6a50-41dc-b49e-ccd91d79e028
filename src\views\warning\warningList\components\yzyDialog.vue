<template>
  <el-dialog title="云资源预警详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">工单编号</div>
        <div class="value text_link" v-if="info.gdId" @click="goDetail">
          {{ info.gdNo }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="line">
        <div class="label">告警id</div>
        <div class="value">{{ info.alarmId || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联部门</div>
        <div class="value">{{ info.deptName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联应用</div>
        <div class="value">{{ info.yyName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警等级</div>
        <div class="value">
          {{
            info.alarmLevel == "P1"
              ? "特别紧急"
              : info.alarmLevel == "P2"
              ? "紧急"
              : info.alarmLevel == "P3"
              ? "一般"
              : "-"
          }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警时间</div>
        <div class="value">{{ info.triggerTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警状态</div>
        <div class="value">
          {{
            info.alarmStatus == 1
              ? "已关闭"
              : info.alarmStatus == 2
              ? "处理中"
              : info.alarmStatus == 3
              ? "告警中"
              : "-"
          }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警类型</div>
        <div class="value">
          {{
            info.alarmType == "tenant_alarm"
              ? "云资源告警"
              : info.alarmType == "apply_alarm"
              ? "应用告警"
              : "-"
          }}
        </div>
      </div>

      <div class="line">
        <div class="label">项目</div>
        <div class="value">{{ info.projectName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">实例名称</div>
        <div class="value">{{ info.monitorObject || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置方式</div>
        <div class="value">
          {{ info.czfs == 1 ? "忽略" : info.czfs == 2 ? "已处置" : "" }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警内容</div>
        <div class="value">{{ info.alarmInfo || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">IP</div>
        <div class="value">{{ info.ip || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">实例ID</div>
        <div class="value">{{ info.alarmSourceId || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警来源</div>
        <div class="value">{{ info.alarmSource || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警源类型</div>
        <div class="value">{{ info.alarmSourceType || "-" }}</div>
      </div>

      <div class="line">
        <div class="label">第一次推送时间</div>
        <div class="value">{{ info.firstPushTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">最近推送时间</div>
        <div class="value">{{ info.lastPushTime || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    goDetail() {
      this.$router.push({
        path: "/serve/workDispose",
        query: { id: this.info.gdId },
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
</style>