<template>
  <el-dialog title="应用监测详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">工单编号</div>
        <div class="value text_link" v-if="info.gdId" @click="goDetail">
          {{ info.gdNo }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="line">
        <div class="label">应用名称</div>
        <div class="value">{{ info.yyName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">系统状态</div>
        <div class="value">
          <div v-if="info.xtStatus == 1">谋划中</div>
          <div v-if="info.xtStatus == 2">建设中</div>
          <div v-if="info.xtStatus == 3">试运行</div>
          <div v-if="info.xtStatus == 4">运行中</div>
          <div v-if="info.xtStatus == 5">停用</div>
        </div>
      </div>
      <div class="line">
        <div class="label">所属部门</div>
        <div class="value">{{ info.deptName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">应用管理员</div>
        <div class="value">{{ info.adminName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">运维截止时间</div>
        <div class="value">{{ info.ywjzsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警状态</div>
        <div class="value">{{ info.status == 1 ?  "处理中" : "已关闭" }}</div>
      </div>
      <div class="line">
        <div class="label">运维厂商</div>
        <div class="value">{{ info.ywcs || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    close() {
      this.$emit("close");
    },
    goDetail() {
      this.$router.push({
        path: "/serve/workDispose",
        query: { id: this.info.gdId },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
</style>