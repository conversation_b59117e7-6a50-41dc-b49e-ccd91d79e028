<template>
  <el-dialog title="基础设施告警详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">工单编号</div>
        <div class="value text_link" v-if="info.gdId" @click="goDetail">
          {{ info.gdNo }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="line">
        <div class="label">告警id</div>
        <div class="value">{{ info.incidentId || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联部门</div>
        <div class="value">{{ info.deptName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联应用</div>
        <div class="value">{{ info.yyName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警等级</div>
        <div class="value">
          <div>
            {{
              info.level == "CRITICAL"
                ? "特别紧急"
                : info.level == "MAJOR"
                ? "紧急"
                : info.level == "MODERATE"
                ? "重要"
                : "一般"
            }}
          </div>
        </div>
      </div>
      <div class="line">
        <div class="label">告警时间</div>
        <div class="value">{{ info.createdTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警状态</div>
        <div class="value">
          {{ info.processStatus == "CLOSED" ? "已关闭" : "处理中" }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警对象</div>
        <div class="value">{{ info.targetName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置人</div>
        <div class="value">{{ info.assignees || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警源名称</div>
        <div class="value">{{ info.alertSourceNames || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">检查项</div>
        <div class="value">{{ info.checks || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">事件名称</div>
        <div class="value">{{ info.incidentNameText || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">检查项</div>
        <div class="value">{{ info.checks || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置方式</div>
        <div class="value">
          {{ info.czfs == 1 ? "忽略" : info.czfs == 2 ? "已处置" : "" }}
        </div>
      </div>
      <div class="line">
        <div class="label">描述</div>
        <div class="value">{{ info.description || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    close() {
      this.$emit("close");
    },
    goDetail() {
      this.$router.push({
        path: "/serve/workDispose",
        query: { id: this.info.gdId },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
</style>