<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="预案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入预案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="noticeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="100" />
      <el-table-column
        label="预案名称"
        align="center"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="场景" align="center" prop="cjms" />
      <el-table-column label="应急通讯录" align="center" prop="createTime">
        <template slot-scope="scope">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <span
              >{{ scope.row.lxr }}
              {{ formatPhone(scope.row.lxfs, scope.row.id) }}</span
            >
            <i
              :class="
                phoneVisibilityMap[scope.row.id]
                  ? 'el-icon-view'
                  : 'el-icon-hide'
              "
              style="margin-left: 8px; cursor: pointer; color: #409eff"
              @click="togglePhoneVisibility(scope.row.id)"
              :title="
                phoneVisibilityMap[scope.row.id] ? '隐藏手机号' : '显示手机号'
              "
            ></i>
            <svg
              t="1754788534305"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="3418"
              width="200"
              height="200"
            >
              <path
                d="M942.208 486.208c-94.784-199.68-238.08-300.16-430.208-300.16-192.192 0-335.36 100.48-430.208 300.224a60.288 60.288 0 0 0 0 51.52c94.784 199.68 238.08 300.16 430.208 300.16 192.192 0 335.36-100.48 430.208-300.224a60.288 60.288 0 0 0 0-51.52zM512 766.016c-161.28 0-279.424-81.792-362.688-254.016C232.576 339.84 350.72 257.984 512 257.984S791.424 339.84 874.688 512c-83.2 172.16-201.28 254.016-362.688 254.016zM507.968 336a176 176 0 1 0 0 352 176 176 0 0 0 0-352z m0 288a112 112 0 1 1 0-224 112 112 0 1 1 0 224z"
                p-id="3419"
              ></path>
            </svg>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleLook(scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案名称" prop="name">
              <el-input
                :disabled="lookFlag"
                v-model="form.name"
                maxlength="20"
                placeholder="请输入预案名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案场景描述" prop="cjms">
              <el-input
                :disabled="lookFlag"
                v-model="form.cjms"
                type="textarea"
                rows="4"
                placeholder="请输入预案场景描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="lxr">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxr"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="lxfs">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxfs"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案内容" prop="content">
              <editor
                :readOnly="lookFlag"
                v-model="form.content"
                :min-height="192"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCzya,
  getCzya,
  delCzya,
  addCzya,
  updateCzya,
} from "@/api/warning/planManage";

export default {
  name: "Notice",
  dicts: ["sys_notice_status", "sys_notice_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
      },
      // 表单参数
      form: {
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      },
      lookFlag: false,
      // 手机号码显示状态控制
      phoneVisibilityMap: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "预案名称不能为空", trigger: "blur" },
        ],
        lxr: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
        lxfs: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "预案内容不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listCzya(this.queryParams).then((response) => {
        this.noticeList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
        // 初始化手机号码显示状态（默认隐藏）
        this.initPhoneVisibility();
      });
    },
    // 初始化手机号码显示状态
    initPhoneVisibility() {
      const visibilityMap = {};
      this.noticeList.forEach((item) => {
        visibilityMap[item.id] = false; // 默认隐藏
      });
      this.phoneVisibilityMap = visibilityMap;
    },
    // 格式化手机号码显示
    formatPhone(phone, id) {
      if (!phone) return "";

      // 检查是否为有效的手机号码格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return phone; // 如果不是标准手机号格式，直接返回原值
      }

      // 根据显示状态决定是否隐藏中间四位
      if (this.phoneVisibilityMap[id]) {
        return phone; // 显示完整手机号
      } else {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2"); // 隐藏中间四位
      }
    },
    // 切换手机号码显示状态
    togglePhoneVisibility(id) {
      this.$set(this.phoneVisibilityMap, id, !this.phoneVisibilityMap[id]);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.lookFlag = false;
      this.open = true;
      this.title = "新增预案";
    },
    /** 查看按钮操作 */
    handleLook(row) {
      this.reset();
      const noticeId = row.id;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = true;
        this.open = true;
        this.title = "查看预案";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.id || this.ids;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = false;
        this.open = true;
        this.title = "编辑预案";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateCzya(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCzya(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm("是否确认删除当前选中的数据项？")
        .then(function () {
          return delCzya({ ids: noticeIds.join(",") });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
