<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="预案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入预案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="noticeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="100" />
      <el-table-column
        label="预案名称"
        align="center"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="场景" align="center" prop="cjms" />
      <el-table-column label="应急通讯录" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ scope.row.lxr }} {{ scope.row.lxfs }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleLook(scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案名称" prop="name">
              <el-input
                :disabled="lookFlag"
                v-model="form.name"
                placeholder="请输入预案名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案场景描述" prop="cjms">
              <el-input
                :disabled="lookFlag"
                v-model="form.cjms"
                type="textarea"
                rows="4"
                placeholder="请输入预案场景描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="lxr">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxr"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="lxfs">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxfs"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案内容" prop='content'>
              <editor
                :readOnly="lookFlag"
                v-model="form.content"
                :min-height="192"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCzya,
  getCzya,
  delCzya,
  addCzya,
  updateCzya,
} from "@/api/warning/planManage";

export default {
  name: "Notice",
  dicts: ["sys_notice_status", "sys_notice_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
      },
      // 表单参数
      form: {
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      },
      lookFlag: false,
      // 表单校验
      rules: {
        name: [
          { required: true, message: "预案名称不能为空", trigger: "blur" },
        ],
        lxr: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
        lxfs: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "预案内容不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listCzya(this.queryParams).then((response) => {
        this.noticeList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.lookFlag = false;
      this.open = true;
      this.title = "新增预案";
    },
    /** 查看按钮操作 */
    handleLook(row) {
      this.reset();
      const noticeId = row.id;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = true;
        this.open = true;
        this.title = "查看预案";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.id || this.ids;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = false;
        this.open = true;
        this.title = "编辑预案";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateCzya(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCzya(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm("是否确认删除当前选中的数据项？")
        .then(function () {
          return delCzya({ ids: noticeIds.join(',') });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
