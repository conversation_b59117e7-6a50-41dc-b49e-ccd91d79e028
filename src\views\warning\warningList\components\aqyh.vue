<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item prop="serialId">
        <el-input
          v-model="queryParams.serialId"
          placeholder="编号"
          @input="handledebounce"
        ></el-input>
      </el-form-item>

      <el-form-item prop="priority">
        <el-select
          v-model="queryParams.priority"
          placeholder="告警等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in priorityOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="告警状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="sjType">
        <el-select
          v-model="queryParams.sjType"
          placeholder="来源"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in sjTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="datalist">
      <el-table-column
        prop="serialId"
        label="编号"
        align="center"
        min-width="110"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="deptName" label="关联部门" align="center" />
      <el-table-column prop="yyName" label="关联应用" align="center" />
      <el-table-column
        prop="priority"
        label="告警等级"
        align="center"
        min-width="60"
      />
      <el-table-column prop="gjsj" label="告警时间" align="center" />
      <el-table-column prop="" label="告警状态" align="center" min-width="100">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="scope.row.status == '完成' ? 'tag_yel' : 'tag_red'"
            >
              {{ scope.row.status }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="告警类型" align="center" min-width="60">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div>
              {{ scope.row.workOrderType == 1 ? "风险隐患" : "安全事件" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="来源" align="center" min-width="60">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.sjType == 1
                  ? 'tag_yel'
                  : scope.row.sjType == 2
                  ? 'tag_red'
                  : ''
              "
            >
              {{ scope.row.sjType == 1 ? "告知" : "通知" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="eventRiskType"
        label="事件或者隐患类型"
        align="center"
        min-width="110"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="initiatingUnit"
        label="发起单位"
        align="center"
        min-width="110"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="securityObject"
        label="对象"
        align="center"
        min-width="110"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="completionUnit"
        label="完成单位"
        align="center"
        min-width="110"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="" label="超时情况" align="center" min-width="60">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.retentionStatus == '超时' ? 'tag_red' : 'tag_gre'
              "
            >
              {{ scope.row.retentionStatus }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="处置方式" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.czfs == 1">忽略</div>
          <div v-if="scope.row.czfs == 2">已处置</div>
        </template></el-table-column
      >
      <el-table-column
        prop="notificationTitle"
        label="标题"
        align="center"
        min-width="130"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          >
          <el-button type="text" disabled v-else>转工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <infoDialog :show="show" :info="info" @close="show = false"></infoDialog>

    <!-- 转工单弹窗 -->
    <addOrderDialog
      :show="show1"
      :info="info1"
      :fjList1="fjList1"
      :deptName1="info1DeptName"
      :deptId1="info1DeptId"
      @close="show1 = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>
  </div>
</template>

<script>
import { listAqyh } from "@/api/warning/warningList";
import infoDialog from "../components/aqyhDialog.vue";
import addOrderDialog from "./addOrderDialog.vue";
import { debounce } from "@/utils";

export default {
  props: {
    yyId: {
      type: Number,
      default: null,
    },
    deptId: {
      type: Number,
      default: null,
    },
    dateArr: {
      type: Array,
      default: [],
    },
  },
  components: { infoDialog, addOrderDialog },
  data() {
    return {
      datalist: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      priorityOptions: [
        { label: "一般", value: "一般" },
        { label: "特别紧急", value: "特别紧急" },
        { label: "紧急", value: "紧急" },
        { label: "重要", value: "重要" },
      ],
      statusOptions: [
        { label: "发起", value: "发起" },
        { label: "审核", value: "审核" },
        { label: "二级单位处置", value: "二级单位处置" },
        { label: "单位处置", value: "单位处置" },
        { label: "终审", value: "终审" },
        { label: "复核", value: "复核" },
        { label: "完成", value: "完成" },
      ],
      sjTypeOptions: [
        { label: "告知", value: 1 },
        { label: "通知", value: 2 },
      ],
      //查看
      show: false,
      info: {},
      //新增工单
      show1: false,
      info1: {},
      info1DeptName: "",
      info1DeptId: null,
      fjList1: [],
    };
  },
  watch: {
    yyId(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
    dateArr(newVal, oldVal) {
      this.getList();
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    //新增工单开始--
    showDialog(row) {
      this.show1 = true;
      this.info1 = {
        gdType: "3",
        title: (row.yyName ? row.yyName : "") + row.eventRiskType,
        describe: row.sjType == 1 ? row.securityDetail : row.callbackRequire,
        priority: 3,
        handlerId: "",
        yyId: row.yyId,
        dataId: row.sjId,
        dataType: 1,
        csList: [],
      };
      console.log(row.fileUrl);
      this.fjList1 =
        row.fileUrl && row.fileUrl.length > 0
          ? row.fileUrl.split(",").filter((item) => item.trim())
          : [];
      this.info1DeptName = row.deptName;
      this.info1DeptId = row.deptId;
    },
    addSuccess() {
      this.show1 = false;
      this.getList();
      this.$forceUpdate();
    },
    //新增工单结束--
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handledebounce() {
      debounce(() => {
        this.handleQuery();
      }, 100);
    },
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        this.datalist = [];
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          priority: this.queryParams.priority,
          status: this.queryParams.status,
          sjType: this.queryParams.sjType,
          serialId: this.queryParams.serialId,
          yyId: this.yyId,
          constructionUnitId: this.deptId,
          startTime: this.dateArr[0],
          endTime: this.dateArr[1],
        };
        const response = await listAqyh(params);
        if (response.code === 200 && response.data) {
          this.datalist = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.datalist = [];
          this.total = 0;
          this.$message.error(response.msg || "获取安全隐患预警列表失败");
        }
      } catch (error) {
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取安全隐患预警列表失败");
      }
    },
    goDetail(row) {
      this.info = row;
      this.show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
.tag_gre {
  background: #36cbcb1a;
  color: #36cbcb;
}
.tag_grey {
  background: #1e222a0d;
  color: #1d2129;
}
</style>
