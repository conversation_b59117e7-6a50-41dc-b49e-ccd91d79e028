import request from "@/utils/request";

// 查应用监控列表
export function listYyJc(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/yyJcList",
    method: "get",
    params: query,
  });
}

// 云资源预警列表(电信)
export function listYzyyj(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/yzyyjList",
    method: "get",
    params: query,
  });
}

// 安全隐患列表
export function listAqyh(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/aqyhList",
    method: "get",
    params: query,
  });
}

// 数据库预警列表
export function listSjkyj(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/sjkyjList",
    method: "get",
    params: query,
  });
}

// 获取详情
export function getGjInfo(query) {
  return request({
    url: "/tyywpt/tygjzxgjlb/getInfo",
    method: "get",
    params: query,
  });
}
