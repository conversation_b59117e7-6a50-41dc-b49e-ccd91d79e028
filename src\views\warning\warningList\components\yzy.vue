<template>
  <div>
    <el-table :data="datalist">
      <el-table-column
        prop="alarmId"
        label="告警id"
        align="center"
        min-width="60"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="departmentName" label="关联部门" align="center" />
      <el-table-column prop="yyName" label="关联应用" align="center" />
      <el-table-column label="告警等级" align="center" min-width="60">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.alarmLevel == 'P1'
                  ? 'tag_red'
                  : scope.row.alarmLevel == 'P2'
                  ? 'tag_org'
                  : scope.row.alarmLevel == 'P3'
                  ? 'tag_yel'
                  : ''
              "
            >
              {{
                scope.row.alarmLevel == "P1"
                  ? "特别紧急"
                  : scope.row.alarmLevel == "P2"
                  ? "紧急"
                  : scope.row.alarmLevel == "P3"
                  ? "一般"
                  : ""
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="triggerTime"
        label="告警时间"
        align="center"
        min-width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="告警状态" align="center" min-width="60">
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.alarmStatus == 1
                  ? 'tag_blu'
                  : scope.row.alarmStatus == 2
                  ? 'tag_yel'
                  : scope.row.alarmStatus == 3
                  ? 'tag_red'
                  : ''
              "
            >
              {{
                scope.row.alarmStatus == 1
                  ? "已关闭"
                  : scope.row.alarmStatus == 2
                  ? "处理中"
                  : scope.row.alarmStatus == 3
                  ? "告警中"
                  : "-"
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="告警类型"
        align="center"
        min-width="60"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <div>
            {{
              scope.row.alarmType == "tenant_alarm"
                ? "云资源告警"
                : scope.row.alarmType == "apply_alarm"
                ? "应用告警"
                : "-"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="项目" align="center" />
      <el-table-column prop="monitorObject" label="实例名称" align="center" />
      <el-table-column prop="" label="处置方式" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.czfs == 1">忽略</div>
          <div v-if="scope.row.czfs == 2">已处置</div>
        </template></el-table-column
      >
      <el-table-column
        prop="alarmInfo"
        label="告警内容"
        align="center"
        min-width="140"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          >
          <el-button type="text" disabled v-else>转工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <infoDialog :show="show" :info="info" @close="show = false"></infoDialog>

    <!-- 转工单弹窗 -->
    <addOrderDialog
      :show="show1"
      :info="info1"
      :deptName1="info1DeptName"
      :deptId1="info1DeptId"
      @close="show1 = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>
  </div>
</template>

<script>
import { listYzyyj } from "@/api/warning/warningList";
import infoDialog from "../components/yzyDialog.vue";
import addOrderDialog from "./addOrderDialog.vue";

export default {
  props: {
    yyId: {
      type: Number,
      default: null,
    },
    deptId: {
      type: Number,
      default: null,
    },
    dateArr: {
      type: Array,
      default: [],
    },
  },
  components: { infoDialog, addOrderDialog },
  data() {
    return {
      datalist: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      //查看
      show: false,
      info: {},
      //新增工单
      show1: false,
      info1: {},
      info1DeptName: "",
      info1DeptId: null,
    };
  },
  watch: {
    yyId(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
    dateArr(newVal, oldVal) {
      this.getList();
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    //新增工单开始--
    showDialog(row) {
      this.show1 = true;
      this.info1 = {
        gdType: "3",
        title: (row.yyName ? row.yyName : "") + "云资源告警",
        describe: row.alarmInfo,
        priority: 3,
        handlerId: "",
        yyId: row.yyId,
        dataId: row.sjId,
        dataType: 2,
        csList: [],
      };
      this.info1DeptName = row.deptName;
      this.info1DeptId = row.deptId;
    },
    addSuccess() {
      this.show1 = false;
      this.getList();
    },
    //新增工单结束--
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        this.datalist = [];
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          yyId: this.yyId,
          constructionUnitId: this.deptId,
          startTime: this.dateArr[0],
          endTime: this.dateArr[1],
        };
        const response = await listYzyyj(params);
        if (response.code === 200 && response.data) {
          this.datalist = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.datalist = [];
          this.total = 0;
          this.$message.error(response.msg || "获取云资源预警列表失败");
        }
      } catch (error) {
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取云资源预警列表失败");
      }
    },
    goDetail(row) {
      this.info = row;
      this.show = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
</style>
