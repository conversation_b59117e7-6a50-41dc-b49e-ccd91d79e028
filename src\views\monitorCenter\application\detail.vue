<template>
  <div class="container" v-loading="loading">
    <div class="tabCon">
      <Tab2
        :tablist="tablist"
        :tabIndex="tabIndex"
        @changeTab="changeTab"
        :systemName="data.systemName"
        :tag="data.tag"
      ></Tab2>
    </div>
    <div class="tabCon-placeholder" v-if="isFixed"></div>
    <div class="card" id="baseInfo">
      <div class="cardTitle">基本信息</div>
      <commonInfo :dataList="jcxx"></commonInfo>
    </div>
    <div class="card" id="health">
      <div class="cardTitle">健康监测</div>
      <health />
    </div>
    <div class="card" id="cloudResource">
      <div class="cardTitle">云资源</div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="ecs" name="ecs"></el-tab-pane>
        <el-tab-pane label="rds" name="rds"></el-tab-pane>
        <el-tab-pane label="slb" name="slb"></el-tab-pane>
        <el-tab-pane label="redis" name="redis"></el-tab-pane>
        <el-tab-pane label="oss" name="oss"></el-tab-pane>
        <el-tab-pane label="polarDbm" name="polarDbm"></el-tab-pane>
        <el-tab-pane label="polarDbo" name="polarDbO"></el-tab-pane>
      </el-tabs>
      <commonTable
        :datalist="yzyLog"
        :keyLabelList="yzyLogKllist"
      ></commonTable>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next"
          :total="yzyTotal"
        ></el-pagination>
      </div>
    </div>
    <div class="card" id="deptInfo">
      <div class="cardTitle">单位信息</div>
      <commonInfo :dataList="dwxx"></commonInfo>
    </div>
    <div class="card" id="ManuInfo">
      <div class="cardTitle">厂商信息</div>
      <commonInfo :dataList="csxx"></commonInfo>
    </div>
    <div class="card" id="SecurityInfo">
      <div class="cardTitle">安全信息</div>
      <commonInfo :dataList="aqxx"></commonInfo>
    </div>
    <!-- <div class="card" id="systemIp">
      <div class="cardTitle">系统地址</div>
      <commonTable :datalist="xtdz" :keyLabelList="xtdzlist"></commonTable>
    </div> -->
    <div class="card" id="relatedProgram">
      <div class="cardTitle">关联项目</div>
      <commonTable :datalist="glxm" :keyLabelList="glxmlist"></commonTable>
    </div>
    <div class="card" id="relatedIp">
      <div class="cardTitle">关联IP</div>
      <commonTable :datalist="glip" :keyLabelList="gliplist"></commonTable>
    </div>
    <div class="card" id="relatedComponent">
      <div class="cardTitle">关联组件</div>
      <commonTable :datalist="glzj" :keyLabelList="glzjlist"></commonTable>
    </div>
    <div class="card" id="projectRequire">
      <div class="cardTitle">项目需求</div>
      <img src="@/assets/images/222.png" alt="" style="width: 100%" />
    </div>
    <el-dialog
      title="云资源详情"
      :visible="show"
      width="70%"
      @close="show = false"
    >
      <commonTable
        :datalist="yzyLog"
        :keyLabelList="yzyLogKllist"
      ></commonTable>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next"
          :total="yzyTotal"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tab2 from "@/views/monitorCenter/application/components/Tab2.vue";
import commonInfo from "@/views/monitorCenter/application/components/commonInfo.vue";
import commonTable from "@/views/monitorCenter/application/components/commonTable.vue";
import baseInfo from "@/views/monitorCenter/application/baseInfo.vue";
import security from "@/views/monitorCenter/application/security.vue";
import contacts from "@/views/monitorCenter/application/contacts.vue";
import deptInfo from "@/views/monitorCenter/application/deptInfo.vue";
import allFields from "@/views/monitorCenter/application/allFields.vue";
import health from "@/views/monitorCenter/application/health";
import {
  getApplication,
  listYzyDetail,
} from "@/api/property/applicationManage";

export default {
  components: {
    Tab2,
    commonInfo,
    commonTable,
    baseInfo,
    security,
    contacts,
    deptInfo,
    allFields,
    health,
  },
  data() {
    return {
      loading: false,
      isFixed: false,
      tabIndex: 0,
      tablist: [
        { name: "基本信息", href: "baseInfo" },
        { name: "健康监测", href: "health" },
        { name: "云资源", href: "cloudResource" },
        { name: "单位信息", href: "deptInfo" },
        { name: "厂商信息", href: "ManuInfo" },
        { name: "安全信息", href: "SecurityInfo" },
        // { name: "系统地址", href: "systemIp" },
        { name: "关联项目", href: "relatedProgram" },
        { name: "关联IP", href: "relatedIp" },
        { name: "关联组件", href: "relatedComponent" },
        { name: "项目需求", href: "projectRequire" },
      ],
      // 应用详情数据
      data: {
        systemName: "",
        tag: [],
      },
      //基础信息
      jcxx: [
        { name: "应用编码", value: "A330701374210202203016675" },

        { name: "应用管理员", value: "程诗悦" },

        { name: "应用类型", value: "业务类" },

        { name: "上线时间", value: "2023/4/1" },

        { name: "运维截止时间", value: "" },

        { name: "运维人员", value: "" },

        { name: "系统状态", value: "运行中" },

        { name: "建设依据", value: "金委办发〔2020〕33 号" },

        { name: "建设层级", value: "市级" },

        { name: "统建范围", value: "市域统建" },

        { name: "用户范围", value: "本级政府用户,地方各级政府用户" },

        { name: "发布端", value: "浙政钉,网页" },

        { name: "网络环境", value: "政务外网" },

        { name: "政务服务", value: "未涉及政务服务事项业务（含公共服务事项）" },

        {
          name: "所属系统",
          value: "党政机关整体智治,数字政府,数字经济,数字社会",
        },

        { name: "履职领域", value: "社会治理" },

        { name: "二级领域", value: "城市运行管理" },

        {
          name: "应用领域",
          value:
            "医疗卫生,社保就业,公共安全,城建住房,交通运输,教育文化,科技创新,生态环境,工业农业,商贸流通,安全生产,市场监管,生活服务,气象服务,地理空间",
        },

        { name: "是否协同", value: "是" },

        { name: "多跨场景", value: "是" },

        { name: "体系贯通", value: "是" },
      ],
      //单位信息
      dwxx: [
        { name: "建设单位", value: "金华市数据局" },

        {
          name: "归口业务处室",
          value: "金华市数据局/数据资源处（数据要素产业处）/数据要素组",
        },

        { name: "单位负责人", value: "程诗悦" },

        { name: "单位类型", value: "党政机关" },

        { name: "电话", value: "" },

        { name: "单位地址", value: "" },
      ],
      //厂商信息
      csxx: [
        { name: "开发厂商", value: "" },

        { name: "开发厂商统一社会信用代码", value: "" },

        { name: "开发厂商联系人", value: "" },

        { name: "开发厂商联系电话", value: "" },

        { name: "运维厂商", value: "" },

        { name: "运维厂商统一社会信用代码", value: "" },

        { name: "运维厂商联系人", value: "" },

        { name: "运维厂商联系电话", value: "" },

        { name: "安全厂商", value: "" },

        { name: "安全厂商统一社会信用代码", value: "" },

        { name: "安全厂商联系人", value: "" },

        { name: "安全厂商联系电话", value: "" },
      ],
      //安全信息
      aqxx: [
        {
          name: "等保级别",
          value: "三级",
        },
        {
          name: "是否等保备案",
          value: "是",
        },
        {
          name: "等保备案时间",
          value: "2022/4/25",
        },
        {
          name: "等保备案编号",
          value: "33070099131-22001",
        },
        {
          name: "等保备案机关",
          value: "金华市公安局",
        },
        {
          name: "是否等保测评",
          value: "是",
        },
        {
          name: "等保测评机构",
          value: "浙江安远检测技术有限公司",
        },
        {
          name: "等保测评得分",
          value: "83.76",
        },
        {
          name: "等保测评时间",
          value: "2024/10/25",
        },
        {
          name: "是否密码测评",
          value: "是",
        },
        {
          name: "密码测评级别",
          value: "三级",
        },
        {
          name: "密码测评时间",
          value: "2024/11/7",
        },
        {
          name: "密码测评编号",
          value: "AXJC241648-MP-01",
        },
      ],
      //系统地址
      xtdzlist: [
        {
          key: "ym",
          label: "域名",
        },
        {
          key: "ip",
          label: "ip",
        },
        {
          key: "dk",
          label: "端口",
        },
        {
          key: "gxsj",
          label: "更新时间",
        },
      ],
      xtdz: [
        {
          ym: "***********",
          ip: "***********",
          dk: "***********",
          gxsj: "2025-06-10",
        },
        {
          ym: "***********",
          ip: "***********",
          dk: "***********",
          gxsj: "2025-06-10",
        },
      ],
      //关联项目
      glxmlist: [
        {
          key: "xmbm",
          label: "项目编码",
        },
        {
          key: "xmmc",
          label: "项目名称",
        },
        {
          key: "jsbm",
          label: "建设部门",
        },
        {
          key: "lxsj",
          label: "立项时间",
        },
      ],
      glxm: [
        {
          xmbm: "12345678",
          xmmc: "项目名称",
          jsbm: "部门名称",
          lxsj: "2023-11-10",
        },
        {
          xmbm: "12345678",
          xmmc: "项目名称",
          jsbm: "部门名称",
          lxsj: "2023-11-10",
        },
      ],
      //关联ip
      gliplist: [
        {
          key: "ipName",
          label: "IP名称",
        },
        {
          key: "ip",
          label: "IP号",
        },
        {
          key: "ipStatus",
          label: "IP状态",
        },
      ],
      glip: [],
      //关联组件
      glzjlist: [
        {
          key: "typeName",
          label: "类型",
        },
        {
          key: "name",
          label: "名称",
        },
        {
          key: "gys",
          label: "供应商",
        },
        {
          key: "bbh",
          label: "版本号",
        },
        {
          key: "zjhxz",
          label: "组件哈希值",
        },
        {
          key: "yqtzjgx",
          label: "与其他组件关系",
        },
        {
          key: "xkxx",
          label: "许可信息",
        },
        {
          key: "xkdqsj",
          label: "许可到期时间",
        },
      ],
      glzj: [],
      //云资源
      yzyKLlist: [
        {
          key: "ecsTotal",
          label: "ecs数量",
          ckey: "ecs",
        },
        {
          key: "rdsTotal",
          label: "rds数量",
          ckey: "rds",
        },
        {
          key: "slbTotal",
          label: "slb数量",
          ckey: "slb",
        },
        {
          key: "redisTotal",
          label: "redis数量",
          ckey: "redis",
        },
        {
          key: "ossTotal",
          label: "oss数量",
          ckey: "oss",
        },
        {
          key: "polarDbmTotal",
          label: "polarDBM数量",
          ckey: "polarDb",
        },
        {
          key: "polarDboTotal",
          label: "poloarDBO数量",
          ckey: "polarDbO",
        },
      ],
      yzy: [],
      activeName: "ecs",
      //云资源详情
      show: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sjType: null,
      },
      yzyTotal: 0,
      yzyLogKllist: [
        {
          key: "uuid",
          label: "实例id",
          width: "120",
          showTooltip: true,
        },
        {
          key: "name",
          label: "实例名称",
          width: "120",
          showTooltip: true,
        },
        {
          key: "sjType",
          label: "实例类型",
          width: "80",
        },
        {
          key: "status",
          label: "状态",
          width: "80",
        },
        {
          key: "vcpu",
          label: "CPU核数",
          width: "80",
        },
        {
          key: "vcpuUtil",
          label: "CPU使用率",
          width: "94",
          unit: "%",
          showUnit: true,
        },
        {
          key: "vmemory",
          label: "内存GB",
          width: "80",
        },
        {
          key: "memoryUtil",
          label: "内存使用率",
          width: "80",
          unit: "%",
          showUnit: true,
        },
        {
          key: "disk",
          label: "存储GB",
          width: "80",
        },
        {
          key: "diskUtil",
          label: "存储使用率",
          width: "80",
          unit: "%",
          showUnit: true,
        },
        {
          key: "ossserverName",
          label: "云区名称",
          width: "100",
          showTooltip: true,
        },
        // {
        //   key: "ip",
        //   label: "IP地址",
        //   width: "120",
        //   showTooltip: true,
        // },
        {
          key: "createTime",
          label: "开通时间",
          width: "106",
          showTooltip: true,
        },
      ],
      yzyLog: [],
    };
  },
  created() {
    this.loadApplicationDetail();
    this.handleClick();
  },
  mounted() {
    window.addEventListener("scroll", this.handleScroll);
  },

  methods: {
    //云资源详情
    yzyItemClick(item) {
      console.log(item);
      this.queryParams.pageNum = 1;
      this.queryParams.sjType = item.ckey;
      this.getYzyList();
    },
    handleClick(tab) {
      console.log("1111111111111", tab);
      this.queryParams.pageNum = 1;
      if (tab) {
        this.activeName = tab.name;
        if (tab.name == "ecs") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },
            {
              key: "vcpu",
              label: "CPU核数",
              width: "80",
            },
            {
              key: "vcpuUtil",
              label: "CPU使用率",
              width: "94",
              unit: "%",
              showUnit: true,
            },
            {
              key: "vmemory",
              label: "内存GB",
              width: "80",
            },
            {
              key: "memoryUtil",
              label: "内存使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "disk",
              label: "存储GB",
              width: "80",
            },
            {
              key: "diskUtil",
              label: "存储使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "rds") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },
            {
              key: "vcpu",
              label: "CPU核数",
              width: "80",
            },
            {
              key: "vcpuUtil",
              label: "CPU使用率",
              width: "94",
              unit: "%",
              showUnit: true,
            },
            {
              key: "vmemory",
              label: "内存GB",
              width: "80",
            },
            {
              key: "memoryUtil",
              label: "内存使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "disk",
              label: "存储GB",
              width: "80",
            },
            {
              key: "diskUtil",
              label: "存储使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "ip",
              label: "IP地址",
              width: "120",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "slb") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "ip",
              label: "IP地址",
              width: "120",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "redis") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },

            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "oss") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "polarDbm") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },
            {
              key: "vcpu",
              label: "CPU核数",
              width: "80",
            },
            {
              key: "vcpuUtil",
              label: "CPU使用率",
              width: "94",
              unit: "%",
              showUnit: true,
            },
            {
              key: "vmemory",
              label: "内存GB",
              width: "80",
            },
            {
              key: "memoryUtil",
              label: "内存使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "disk",
              label: "存储GB",
              width: "80",
            },
            {
              key: "diskUtil",
              label: "存储使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "ip",
              label: "IP地址",
              width: "120",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        } else if (tab.name == "polarDbo") {
          this.yzyLogKllist = [
            {
              key: "uuid",
              label: "实例id",
              width: "120",
              showTooltip: true,
            },
            {
              key: "name",
              label: "实例名称",
              width: "120",
              showTooltip: true,
            },
            {
              key: "sjType",
              label: "实例类型",
              width: "80",
            },
            {
              key: "status",
              label: "状态",
              width: "80",
            },
            {
              key: "vcpu",
              label: "CPU核数",
              width: "80",
            },
            {
              key: "vcpuUtil",
              label: "CPU使用率",
              width: "94",
              unit: "%",
              showUnit: true,
            },
            {
              key: "vmemory",
              label: "内存GB",
              width: "80",
            },
            {
              key: "memoryUtil",
              label: "内存使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "disk",
              label: "存储GB",
              width: "80",
            },
            {
              key: "diskUtil",
              label: "存储使用率",
              width: "80",
              unit: "%",
              showUnit: true,
            },
            {
              key: "ossserverName",
              label: "云区名称",
              width: "100",
              showTooltip: true,
            },
            {
              key: "ip",
              label: "IP地址",
              width: "120",
              showTooltip: true,
            },
            {
              key: "createTime",
              label: "开通时间",
              width: "106",
              showTooltip: true,
            },
          ];
        }
      }
      this.getYzyList();
    },
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getYzyList();
    },
    // 云资源详情列表
    getYzyList() {
      let params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        sjType: this.activeName,
        yyId: this.$route.params.id || this.$route.query.id,
      };
      listYzyDetail(params).then((res) => {
        console.log("listYzyDetail response:", res);
        this.yzyLog = res.data.list;
        this.yzyTotal = res.data.total;
      });
    },
    // 加载应用详情数据
    async loadApplicationDetail() {
      try {
        this.loading = true;
        const applicationId = this.$route.params.id || this.$route.query.id;

        if (!applicationId) {
          this.$message.error("应用ID不能为空");
          return;
        }

        console.log("加载应用详情，ID:", applicationId);

        const response = await getApplication(applicationId);
        console.log("应用详情API响应:", response);

        if (response.code === 200 && response.data) {
          // 映射API数据到页面数据结构
          this.data = {
            systemName: response.data.name || "",
            tag: response.data.tag ? response.data.tag.split(",") : [],
          };

          // 映射基本信息
          this.mapBasicInfo(response.data);
          // 映射单位信息
          this.mapDeptInfo(response.data);
          // 映射厂商信息
          this.mapVendorInfo(response.data);
          // 映射安全信息
          this.mapSecurityInfo(response.data);
          // 映射关联数据
          this.mapRelatedData(response.data);

          console.log("应用详情数据加载完成:", this.data);
        } else {
          console.error("获取应用详情失败:", response.msg);
          this.$message.error(response.msg || "获取应用详情失败");
        }
      } catch (error) {
        console.error("加载应用详情异常:", error);
        this.$message.error("加载应用详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 映射基本信息
    mapBasicInfo(data) {
      this.jcxx = [
        { name: "应用编码", value: data.yybm || "" },
        { name: "应用管理员", value: data.adminName || "" },
        { name: "应用类型", value: this.getYylxText(data.yylx) || "" },
        { name: "上线时间", value: data.sxsj || "" },
        { name: "运维截止时间", value: data.ywjzsj || "" },
        { name: "系统状态", value: this.getStatusText(data.xtStatus) || "" },
        { name: "建设依据", value: this.getJsyjText(data.jsyj) || "" },
        { name: "政策文件", value: data.zcwj || "" },
        { name: "建设层级", value: this.getJscjText(data.jscj) || "" },
        { name: "是否统筹", value: this.getBoolText(data.sftc) || "" },
        { name: "用户范围", value: this.getYhfwText(data.yhfw) || "" },
        { name: "发布端", value: this.getFbdText(data.fbd) || "" },
        { name: "网络环境", value: this.getWlhjText(data.wlhj) || "" },
        { name: "所属系统", value: this.getSshjText(data.sshj) || "" },
        { name: "应用领域", value: this.getYylyText(data.yyly) || "" },
        {
          name: "是否协同",
          value: this.getBoolText(data.isCollaboration) || "",
        },
        { name: "跨场景", value: this.getBoolText(data.isCrossScenario) || "" },
        {
          name: "系统互通",
          value: this.getBoolText(data.isSystemConnected) || "",
        },
        { name: "系统网址", value: data.systemUrl || "" },
        { name: "系统域名", value: data.url || "" },
      ];
    },

    // 映射单位信息
    mapDeptInfo(data) {
      this.dwxx = [
        { name: "建设单位", value: data.deptName || "" },
        { name: "业务部门", value: data.businessDepartment || "" },
        { name: "单位负责人", value: data.unitLeader || "" },
        { name: "单位类型", value: this.getUnitTypeText(data.unitType) || "" },
        { name: "联系电话", value: data.phone || "" },
        { name: "单位地址", value: data.address || "" },
      ];
    },

    // 映射厂商信息
    mapVendorInfo(data) {
      this.csxx = [
        { name: "开发厂商", value: data.kfcs || "" },
        { name: "开发厂商统一社会信用代码", value: data.kfcstyxydm || "" },
        { name: "开发厂商联系人", value: data.kfclxr || "" },
        { name: "开发厂商联系电话", value: data.kfclxdh || "" },
        { name: "运维厂商", value: data.ywcs || "" },
        { name: "运维厂商统一社会信用代码", value: data.ywcstyxydm || "" },
        { name: "运维厂商联系人", value: data.ywclxr || "" },
        { name: "运维厂商联系电话", value: data.ywclxdh || "" },
        { name: "安全厂商", value: data.aqcs || "" },
        { name: "安全厂商统一社会信用代码", value: data.aqcstyxydm || "" },
        { name: "安全厂商联系人", value: data.aqclxr || "" },
        { name: "安全厂商联系电话", value: data.aqclxdh || "" },
      ];
    },

    // 映射安全信息
    mapSecurityInfo(data) {
      this.aqxx = [
        {
          name: "等保级别",
          value: this.getDbSecurityLevelText(data.dbSecurityLevel) || "",
        },
        {
          name: "是否等保备案",
          value: this.getBoolText(data.dbIsSecurityFiling) || "",
        },
        { name: "等保备案时间", value: data.dbSecurityFilingDate || "" },
        { name: "等保备案编号", value: data.dbSecurityFilingNo || "" },
        { name: "等保备案机关", value: data.dbSecurityFilingOrg || "" },
        {
          name: "是否等保测评",
          value: this.getBoolText(data.dbIsSecurityAssessment) || "",
        },
        { name: "等保测评机构", value: data.dbSecurityAssessmentOrg || "" },
        { name: "等保测评得分", value: data.dbSecurityAssessmentScore || "" },
        { name: "等保测评时间", value: data.dbSecurityAssessmentDate || "" },
        {
          name: "是否密码测评",
          value: this.getBoolText(data.dbIsCryptoAssessment) || "",
        },
        {
          name: "密码测评级别",
          value:
            this.getDbCryptoAssessmentLevelText(data.dbCryptoAssessmentLevel) ||
            "",
        },
        { name: "密码测评时间", value: data.dbCryptoAssessmentDate || "" },
        { name: "密码测评编号", value: data.dbCryptoAssessmentNo || "" },
      ];
    },

    // 映射关联数据
    mapRelatedData(data) {
      // 云资源
      this.yzy = data.dxZytj ? [data.dxZytj] : [];
      // 系统地址
      this.xtdz = data.xtList || [];
      // 关联项目
      this.glxm = data.xmList || [];
      // 关联IP
      this.glip =
        data.ipList.map((item) => {
          return {
            ipName: item.ipName,
            ip: item.ip,
            ipStatus:
              item.ipStatus == "1"
                ? "运行"
                : item.ipStatus == "2"
                ? "关闭"
                : "",
          };
        }) || [];
      this.glzj =
        data.zjList.map((item) => {
          return {
            bbh: item.bbh,
            gys: item.gys,
            name: item.name,
            zjhxz: item.zjhxz,
            yqtzjgx: item.yqtzjgx,
            xkxx: item.xkxx,
            xkdqsj: item.xkdqsj,
            typeName:
              item.type == "1"
                ? "中间件"
                : item.ipStatus == "2"
                ? "系统"
                : item.ipStatus == "3"
                ? "数据库"
                : "证书",
          };
        }) || [];
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "谋划中",
        2: "建设中",
        3: "试运行",
        4: "运行中",
        5: "停用",
      };
      return statusMap[status] || "未知";
    },

    // 获取应用类型文本
    getYylxText(value) {
      const yylxMap = {
        1: "办公后勤类",
        2: "业务类",
        3: "信息宣传类",
        4: "基础支撑类",
      };
      return yylxMap[value] || "";
    },

    // 获取建设依据文本
    getJsyjText(value) {
      const jsyjMap = {
        1: "政策文件",
        2: "领导讲话及批示",
        3: "部门内部会议/文件",
        4: "其他",
      };
      return jsyjMap[value] || "";
    },

    // 获取建设层级文本
    getJscjText(value) {
      const jscjMap = {
        1: "国家",
        2: "省级",
        3: "市级",
        4: "县(市、区)",
        5: "乡镇(街道)",
        6: "村(社区)",
      };
      return jscjMap[value] || "";
    },

    // 获取布尔值文本
    getBoolText(value) {
      const boolMap = {
        0: "否",
        1: "是",
      };
      return boolMap[value] || "";
    },

    // 获取用户范围文本
    getYhfwText(value) {
      if (!value) return "";
      const yhfwMap = {
        1: "处室内部",
        2: "本部门内部",
        3: "本级政府用户",
        4: "地方各级政府用户",
        5: "社会公众(面向个人)",
        6: "社会公众(面向法人)",
      };

      // 如果是字符串且包含逗号，说明是多个值
      if (typeof value === "string" && value.includes(",")) {
        return value
          .split(",")
          .map((v) => yhfwMap[parseInt(v.trim())] || v.trim())
          .join(",");
      }

      return yhfwMap[value] || value;
    },

    // 获取发布端文本
    getFbdText(value) {
      if (!value) return "";
      const fbdMap = {
        1: "浙里办",
        2: "浙政钉",
        3: "数字化改革门户",
        4: "支付宝",
        5: "微信",
        6: "网页",
        7: "园区PC客户端",
        8: "APP端",
        9: "政务服务网",
      };

      // 如果是字符串且包含逗号，说明是多个值
      if (typeof value === "string" && value.includes(",")) {
        return value
          .split(",")
          .map((v) => fbdMap[parseInt(v.trim())] || v.trim())
          .join(",");
      }

      return fbdMap[value] || value;
    },

    // 获取网络环境文本
    getWlhjText(value) {
      const wlhjMap = {
        0: "是否互联网系统",
        1: "政务内网",
        2: "政务外网",
        3: "互联网",
        4: "业务专网",
        5: "单机",
      };
      return wlhjMap[value] || "";
    },

    // 获取所属系统文本
    getSshjText(value) {
      if (!value) return "";
      const sshjMap = {
        1: "党政机关整体智治",
        2: "数字政府",
        3: "数字经济",
        4: "数字社会",
        5: "数字法治",
        6: "数字文化",
      };

      // 如果是字符串且包含逗号，说明是多个值
      if (typeof value === "string" && value.includes(",")) {
        return value
          .split(",")
          .map((v) => sshjMap[parseInt(v.trim())] || v.trim())
          .join(",");
      }

      return sshjMap[value] || value;
    },

    // 获取应用领域文本
    getYylyText(value) {
      if (!value) return "";
      const yylyMap = {
        1: "信用服务",
        2: "财税金融",
        3: "医疗卫生",
        4: "安全生产",
        5: "社保就业",
        6: "市场监管",
        7: "公共安全",
        8: "社会救助",
        9: "城建住房",
        10: "法律服务",
        11: "交通运输",
        12: "生活服务",
        13: "教育文化",
        14: "气象服务",
        15: "科技创新",
        16: "地理空间",
        17: "资源能源",
        18: "机构团体",
        19: "生态环境",
        20: "其他",
        21: "工业农业",
        22: "商贸流通",
      };

      // 如果是字符串且包含逗号，说明是多个值
      if (typeof value === "string" && value.includes(",")) {
        return value
          .split(",")
          .map((v) => yylyMap[parseInt(v.trim())] || v.trim())
          .join(",");
      }

      return yylyMap[value] || value;
    },

    // 获取等保级别文本
    getDbSecurityLevelText(value) {
      const dbSecurityLevelMap = {
        1: "一级",
        2: "二级",
        3: "三级",
      };
      return dbSecurityLevelMap[value] || "";
    },

    // 获取密码测评级别文本
    getDbCryptoAssessmentLevelText(value) {
      const dbCryptoAssessmentLevelMap = {
        1: "一级",
        2: "二级",
        3: "三级",
        4: "四级",
        5: "五级",
      };
      return dbCryptoAssessmentLevelMap[value] || "";
    },

    // 获取单位类型文本
    getUnitTypeText(value) {
      // 如果value已经是文本，直接返回
      if (typeof value === "string" && value !== "") {
        return value;
      }
      // 如果是数字，进行映射
      const unitTypeMap = {
        1: "党政机关",
        2: "国企",
      };
      return unitTypeMap[value] || "";
    },

    ObjectToArray() {
      return Object.keys(this.data).map((key) => {
        return {
          name: key,
          value: this.data[key],
        };
      });
    },
    changeTab(e) {
      this.tabIndex = e.index;
      const targetId = e.href;
      const target = document.getElementById(targetId);

      if (target) {
        const offset = 12;
        const targetPosition = target.offsetTop - offset;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });

        // 更新URL hash（不触发路由跳转）
        history.replaceState(null, null, `#${targetId}`);
      }
    },
    handleScroll() {
      const tabCon = document.querySelector(".tabCon");
      if (window.scrollY > 100) {
        tabCon.classList.add("fixed");
        this.isFixed = true;
      } else {
        tabCon.classList.remove("fixed");
        this.isFixed = false;
      }
    },
    goBack() {
      this.$router.go(-1);
    },
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll);
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 60px;
  box-sizing: border-box;
  position: relative;
  .tabCon {
    position: relative;
    z-index: 10;
    // padding: 0 20px;
    // box-sizing: border-box;
  }
  .tabCon.fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 0 20px;
    box-sizing: border-box;
  }
  .tabCon-placeholder {
    width: 100%;
    height: 107px;
  }
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-top: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>
